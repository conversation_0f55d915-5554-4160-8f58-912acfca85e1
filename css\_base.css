/* 包含 Pagetalk 插件的基础样式、全局布局（如 body, header, footer）、通用组件（如按钮、模态框）和滚动条样式。 */

/* 基本样式重置 */
   * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}
/* 列表缩进 */
ul, ol {
    padding-left: 20px; /* 默认缩进 */
}

ul ul, ol ol, ul ol, ol ul {
    padding-left: 20px; /* 嵌套列表增加缩进 */
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    height: 100vh;
    width: 100%;
    overflow: hidden;
    font-size: 14px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    /* border-left: 1px solid var(--border-color); /* 移除左侧边框 */
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 100%;
    margin: 0 auto;
}

/* 头部样式 */
header {
    background-color: var(--card-background);
    color: var(--text-color);
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    min-height: fit-content;
}

header h1 {
    font-size: clamp(16px, 4vw, 18px); /* 响应式字体大小 */
    margin-bottom: var(--spacing-xs);
    font-weight: 600;
    letter-spacing: -0.5px;
    color: var(--primary-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 主要内容区域 */
main {
    flex: 1;
    overflow: hidden; /* Prevent main itself from scrolling */
    position: relative;
    min-height: 0; /* 确保内容区域可以缩小 */
}

/* 标签页内容区域 */
.tab-content {
    display: none;
    height: 100%; 
    width: 100%;
    overflow-x: hidden;
}

.tab-content.active {
    display: flex; /* Use flex for layout */
    flex-direction: column; /* Stack content vertically */
    height: 100%; /* Ensure it takes full height of main */
    overflow: hidden; /* Prevent the section itself from scrolling */
}


/* 页脚样式 - 添加顶部导航 */
footer {
    background-color: var(--card-background);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 11px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 底部状态栏 */
.status-bar {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
    color: var(--text-secondary);
}

/* 底部标签页 */
.footer-tabs {
    display: flex;
    list-style: none;
    gap: var(--spacing-md); /* Reduced gap */
    margin-left: auto;
}

.footer-tab {
    padding: var(--spacing-xs) var(--spacing-sm); /* Adjusted padding */
    cursor: pointer;
    position: relative;
    color: var(--text-secondary);
    transition: all 0.2s ease; /* Faster transition */
    font-weight: 500;
    font-size: 11px;
    border-radius: var(--radius-md);
    background-color: transparent; /* Default transparent */
    border: 1px solid transparent; /* Add transparent border for layout consistency */
}

.footer-tab:hover {
    color: var(--primary-color);
    background-color: var(--button-hover-bg); /* Use standard hover */
}

.footer-tab.active {
    color: var(--primary-color);
    font-weight: 600;
    background-color: rgba(76, 110, 245, 0.15); /* Use slightly darker active background */
    border-color: rgba(76, 110, 245, 0.2); /* Add subtle border when active */
}

/* 连接指示器 */
#connection-indicator {
    padding: 1px 6px;
    border-radius: var(--radius-full);
    font-size: 10px;
    display: inline-block;
}

#connection-indicator.connected {
    background-color: rgba(52, 168, 83, 0.15);
    color: var (--success-color);
}

#connection-indicator.disconnected {
    background-color: rgba(234, 67, 53, 0.15);
    color: var(--error-color);
}

/* 加载动画 */
.loading {
    display: inline-block;
    position: relative;
    width: 20px;
    height: 20px;
}

.loading div {
    position: absolute;
    background-color: var(--primary-color);
    opacity: 1;
    border-radius: 50%;
    animation: loading 1.4s cubic-bezier(0, 0.5, 0.5, 1) infinite;
}

.loading div:nth-child(1) {
    top: 8px;
    left: 0;
    animation-delay: -0.30s;
}

.loading div:nth-child(2) {
    top: 8px;
    left: 8px;
    animation-delay: -0.15s;
}

.loading div:nth-child(3) {
    top: 8px;
    left: 16px;
    animation-delay: 0s;
}

@keyframes loading {
    0% { transform: scale(0); }
    50% { transform: scale(1); }
    100% { transform: scale(0); }
}

/* 通用操作按钮 (例如聊天头部的清除按钮，设置页的关闭按钮) */
.action-button {
    background: transparent;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width: clamp(24px, 7vw, 28px); 
    height: clamp(24px, 7vw, 28px); 
    border-radius: var(--radius-full);
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s ease;
    padding: 0;
    flex-shrink: 0; /* 防止按钮被压缩 */
}

.action-button:hover {
    color: var(--error-color); /* 默认悬停为错误色，特定按钮可覆盖 */
    background-color: rgba(234, 67, 53, 0.1);
}

.action-button svg {
    width: 16px; 
    height: 16px; 
}

/* 关闭面板按钮特定样式 */
.close-panel-btn {
  color: var(--text-secondary);
}

.close-panel-btn:hover {
  color: var(--error-color);
  background-color: rgba(234, 67, 53, 0.1);
}

/* 单独增大关闭按钮尺寸 */
.close-panel-btn {
    width: clamp(28px, 8vw, 32px);  /* 增大按钮尺寸 */
    height: clamp(28px, 8vw, 32px); /* 增大按钮尺寸 */
}

/* 单独增大并加粗关闭按钮图标 */
.close-panel-btn svg {
    width: clamp(18px, 5vw, 20px); /* 增大图标尺寸 */
    height: clamp(18px, 5vw, 20px); /* 增大图标尺寸 */
    stroke-width: 2; /* 加粗线条 */
    transition: all 0.2s ease; /* 添加过渡效果 */
}

/* 通用图标按钮 (例如 Agent 设置中的导入/导出/添加按钮) */
.icon-btn {
    width: clamp(28px, 8vw, 32px);  /* 增大按钮尺寸 */
    height: clamp(28px, 8vw, 32px); /* 增大按钮尺寸 */
    border-radius: var(--radius-full);
    border: none;
    background-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-secondary);
    flex-shrink: 0;
}

.icon-btn:hover {
    background-color: var(--button-hover-bg); /* 使用标准悬停背景 */
    color: var(--primary-color);
}

.icon-btn svg {
    width: clamp(16px, 4.5vw, 18px); /* 增大图标尺寸 */
    height: clamp(16px, 4.5vw, 18px); /* 增大图标尺寸 */
    transition: all 0.2s ease; /* 添加过渡效果 */
}


/* 滚动条样式 */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: var(--radius-full);
    border: 2px solid var(--background-color); /* Add a border to make it look slightly thinner but easier to grab */
}

::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover);
}

/* 更新通告模态框样式 - 精简版 */
.changelog-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: auto;
    animation: fadeIn 0.3s ease;
}

.changelog-content {
    position: relative;
    background-color: var(--card-background);
    margin: 12% auto; /* 位置再向上移 */
    padding: var(--spacing-lg); /* 增大内边距 */
    width: 85%;
    max-width: 400px;
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    animation: slideInUp 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.changelog-header {
    margin-bottom: var(--spacing-md); /* 增大头部下边距 */
    text-align: center;
}

.changelog-header h2 {
    font-size: 1.4rem; /* 增大标题字体 */
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.changelog-list {
    max-height: 220px; /* 略微增大最大高度 */
    overflow-y: auto;
    margin-bottom: var(--spacing-md); /* 增大底部边距 */
}

.changelog-item {
    margin-bottom: var(--spacing-md); /* 增大项目间距 */
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.changelog-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.changelog-version {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-md); /* 增大版本信息与内容的间距 */
}

.changelog-version-number {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1.1rem; /* 增大版本号字体 */
}

.changelog-version-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.changelog-changes {
    margin-left: var(--spacing-md);
    font-size: 0.95rem; /* 略微增大更新内容字体 */
}

.changelog-changes li {
    margin-bottom: var(--spacing-md); /* 增大列表项间距 */
    line-height: 1.5; /* 增大行高 */
}

.changelog-changes li:last-child {
    margin-bottom: 0; /* 移除最后一项的底部边距 */
}

.changelog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-lg); /* 增大页脚上边距 */
    padding-top: var(--spacing-md); /* 添加顶部内边距 */
    border-top: 1px solid var(--border-color); /* 添加顶部边框 */
}

.changelog-checkbox {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm); /* 增大复选框与文本的间距 */
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.changelog-checkbox input[type="checkbox"] {
    accent-color: var(--primary-color);
    width: 16px; /* 增大复选框尺寸 */
    height: 16px;
}

.changelog-ok-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-lg); /* 增大按钮内边距 */
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px; /* 设置最小宽度 */
    text-align: center;
}

.changelog-ok-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

/* 确保所有内容容器不会溢出父容器 */
.tab-content,
.container,
main {
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: hidden; /* 通常 x 轴不应该滚动 */
}

/* 确保图片不会溢出容器 */
img {
    max-width: 100%;
    height: auto;
}

/* 确保所有按钮在窄屏上能够正常显示 */
button {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}