/* Pagetalk 侧边栏主样式表 - 导入模块化的 CSS 文件 */

/* 导入 CSS 变量定义 */
@import url("_variables.css");
/* 导入基础样式、全局布局和通用组件 */
@import url("_base.css");
/* 导入 Markdown 内容渲染样式 */
@import url("_markdown.css");
/* 导入聊天界面相关样式 */
@import url("_chat.css");
/* 导入设置界面相关样式 */
@import url("_settings.css");
/* 导入划词助手相关样式 */
@import url("text-selection-helper.css");
/* 导入深色模式特定样式 */
@import url("_dark-mode.css");
/* 导入响应式布局样式 */
@import url("_responsive.css");

/* 如果有非常少量的全局覆盖或工具类可以在此定义，但优先使用模块。 */