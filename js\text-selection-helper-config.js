/**
 * PageTalk - Text Selection Helper Configuration Module
 * 划词助手配置管理模块
 * 
 * 统一管理默认设置，消除重复的配置定义
 */

/**
 * 默认模型配置
 */
const DEFAULT_MODELS = {
    INTERPRET: 'gemini-2.5-flash',
    TRANSLATE: 'gemini-2.5-flash',
    CHAT: 'gemini-2.5-flash'
};

/**
 * 默认温度设置
 */
const DEFAULT_TEMPERATURES = {
    INTERPRET: 0.7,
    TRANSLATE: 0.2,
    CHAT: 0.7
};

/**
 * 默认上下文窗口设置
 */
const DEFAULT_CONTEXT = {
    BEFORE: 500,
    AFTER: 500
};

/**
 * 默认输出长度限制
 */
const DEFAULT_MAX_OUTPUT_LENGTH = 65536;

/**
 * 默认选项顺序
 */
const DEFAULT_OPTIONS_ORDER = ['interpret', 'translate', 'chat'];

/**
 * 窗口默认尺寸配置
 */
const WINDOW_DEFAULT_SIZE = {
    width: () => window.innerWidth * 0.35,
    height: 300,
    chatHeight: 450,
    minWidth: 400,
    minHeight: 250
};

/**
 * Mini Icon 偏移配置
 */
const MINI_ICON_OFFSET = { x: -20, y: 5 };

/**
 * 创建默认设置的工厂函数
 * @param {string} language - 语言代码，默认为 'zh-CN'
 * @returns {Object} 默认设置对象
 */
function createDefaultSettings(language = 'zh-CN') {
    // 获取语言相关的默认提示词
    const interpretPrompt = getDefaultPrompt('interpret', language);
    const translatePrompt = getDefaultPrompt('translate', language);

    return {
        enabled: true,
        interpret: {
            model: DEFAULT_MODELS.INTERPRET,
            systemPrompt: interpretPrompt,
            temperature: DEFAULT_TEMPERATURES.INTERPRET,
            contextBefore: DEFAULT_CONTEXT.BEFORE,
            contextAfter: DEFAULT_CONTEXT.AFTER,
            maxOutputLength: DEFAULT_MAX_OUTPUT_LENGTH
        },
        translate: {
            model: DEFAULT_MODELS.TRANSLATE,
            systemPrompt: translatePrompt,
            temperature: DEFAULT_TEMPERATURES.TRANSLATE,
            contextBefore: DEFAULT_CONTEXT.BEFORE,
            contextAfter: DEFAULT_CONTEXT.AFTER,
            maxOutputLength: DEFAULT_MAX_OUTPUT_LENGTH
        },
        customOptions: [],
        optionsOrder: [...DEFAULT_OPTIONS_ORDER]
    };
}

/**
 * 获取默认提示词
 * @param {string} type - 提示词类型 ('interpret' | 'translate')
 * @param {string} language - 语言代码
 * @returns {string} 默认提示词
 */
function getDefaultPrompt(type, language) {
    // 优先使用全局的默认提示词函数
    if (window.getDefaultPrompt && typeof window.getDefaultPrompt === 'function') {
        return window.getDefaultPrompt(type, language);
    }

    // 回退到硬编码的默认值
    const fallbackPrompts = {
        'zh-CN': {
            interpret: '解读一下',
            translate: '翻译一下'
        },
        'en': {
            interpret: 'Interpret this',
            translate: 'Translate this'
        }
    };

    return fallbackPrompts[language]?.[type] || fallbackPrompts['zh-CN'][type];
}

/**
 * 创建聊天默认设置
 * @param {string} language - 语言代码
 * @returns {Object} 聊天设置对象
 */
function createChatDefaultSettings(language = 'zh-CN') {
    return {
        model: DEFAULT_MODELS.CHAT,
        temperature: DEFAULT_TEMPERATURES.CHAT,
        maxOutputLength: DEFAULT_MAX_OUTPUT_LENGTH
    };
}

/**
 * 验证设置对象的完整性
 * @param {Object} settings - 设置对象
 * @returns {boolean} 是否有效
 */
function validateSettings(settings) {
    if (!settings || typeof settings !== 'object') {
        return false;
    }

    // 检查必需的属性
    const requiredProps = ['interpret', 'translate'];
    for (const prop of requiredProps) {
        if (!settings[prop] || typeof settings[prop] !== 'object') {
            return false;
        }

        // 检查每个选项的必需属性
        const requiredOptionProps = ['model', 'systemPrompt', 'temperature'];
        for (const optionProp of requiredOptionProps) {
            if (settings[prop][optionProp] === undefined) {
                return false;
            }
        }
    }

    return true;
}

/**
 * 合并设置，用默认值填充缺失的属性
 * @param {Object} userSettings - 用户设置
 * @param {string} language - 语言代码
 * @returns {Object} 合并后的完整设置
 */
function mergeWithDefaults(userSettings, language = 'zh-CN') {
    const defaultSettings = createDefaultSettings(language);
    
    if (!userSettings || typeof userSettings !== 'object') {
        return defaultSettings;
    }

    // 深度合并设置
    const merged = { ...defaultSettings };
    
    // 合并基本属性
    if (typeof userSettings.enabled === 'boolean') {
        merged.enabled = userSettings.enabled;
    }

    // 合并选项设置
    ['interpret', 'translate'].forEach(option => {
        if (userSettings[option] && typeof userSettings[option] === 'object') {
            merged[option] = { ...merged[option], ...userSettings[option] };
        }
    });

    // 合并自定义选项
    if (Array.isArray(userSettings.customOptions)) {
        merged.customOptions = userSettings.customOptions;
    }

    // 合并选项顺序
    if (Array.isArray(userSettings.optionsOrder)) {
        merged.optionsOrder = userSettings.optionsOrder;
    }

    return merged;
}

/**
 * 获取模型选项列表
 * @returns {Array} 模型选项数组
 */
function getModelOptions() {
    return [
        { value: 'gemini-2.0-flash', text: 'gemini-2.0-flash' },
        { value: 'gemini-2.5-flash', text: 'gemini-2.5-flash' },
        { value: 'gemini-2.5-flash-thinking', text: 'gemini-2.5-flash-thinking' },
        { value: 'gemini-2.0-flash-thinking-exp-01-21', text: 'gemini-2.0-flash-thinking' },
        { value: 'claude-3-5-sonnet-20241022', text: 'claude-3.5-sonnet' },
        { value: 'claude-3-5-haiku-20241022', text: 'claude-3.5-haiku' },
        { value: 'gpt-4o', text: 'gpt-4o' },
        { value: 'gpt-4o-mini', text: 'gpt-4o-mini' }
    ];
}

// 导出配置对象
window.TextSelectionHelperConfig = {
    // 常量
    DEFAULT_MODELS,
    DEFAULT_TEMPERATURES,
    DEFAULT_CONTEXT,
    DEFAULT_MAX_OUTPUT_LENGTH,
    DEFAULT_OPTIONS_ORDER,
    WINDOW_DEFAULT_SIZE,
    MINI_ICON_OFFSET,
    
    // 函数
    createDefaultSettings,
    createChatDefaultSettings,
    getDefaultPrompt,
    validateSettings,
    mergeWithDefaults,
    getModelOptions
};

console.log('[TextSelectionHelperConfig] Configuration module loaded');
