/**
 * Panzoom for panning and zooming elements using CSS transforms
 * Copyright <PERSON><PERSON> and other contributors
 * https://github.com/timmywil/panzoom/blob/master/MIT-License.txt
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).Panzoom=t()}(this,function(){"use strict";var r,T=function(){return(T=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)};function A(e,t){for(var n=e.length;n--;)if(e[n].pointerId===t.pointerId)return n;return-1}function L(e,t){var n;if(t.touches)for(var o=n=0,r=t.touches;o<r.length;o++){var a=r[o];a.pointerId=n++,L(e,a)}else-1<(n=A(e,t))&&e.splice(n,1),e.push(t)}function I(e){for(var t,n=(e=e.slice(0)).pop();t=e.pop();)n={clientX:(t.clientX-n.clientX)/2+n.clientX,clientY:(t.clientY-n.clientY)/2+n.clientY};return n}function W(e){if(e.length<2)return 0;var t=e[0],n=e[1];return Math.sqrt(Math.pow(Math.abs(n.clientX-t.clientX),2)+Math.pow(Math.abs(n.clientY-t.clientY),2))}function Z(e,t,n,o){r[e].split(" ").forEach(function(e){t.addEventListener(e,n,o)})}function q(e,t,n){r[e].split(" ").forEach(function(e){t.removeEventListener(e,n)})}window.NodeList&&!NodeList.prototype.forEach&&(NodeList.prototype.forEach=Array.prototype.forEach),"function"!=typeof window.CustomEvent&&(window.CustomEvent=function(e,t){t=t||{bubbles:!1,cancelable:!1,detail:null};var n=document.createEvent("CustomEvent");return n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),n}),r="function"==typeof window.PointerEvent?{down:"pointerdown",move:"pointermove",up:"pointerup pointerleave pointercancel"}:"function"==typeof window.TouchEvent?{down:"touchstart",move:"touchmove",up:"touchend touchcancel"}:{down:"mousedown",move:"mousemove",up:"mouseup mouseleave"};var a=document.createElement("div").style,i=["webkit","moz","ms"],l={};function p(e){if(l[e])return l[e];if(e in a)return l[e]=e;for(var t=e[0].toUpperCase()+e.slice(1),n=i.length;n--;){var o=""+i[n]+t;if(o in a)return l[e]=o}}function s(e,t){return parseFloat(t[p(e)])||0}function c(e,t,n){void 0===n&&(n=window.getComputedStyle(e));var o="border"===t?"Width":"";return{left:s(t+"Left"+o,n),right:s(t+"Right"+o,n),top:s(t+"Top"+o,n),bottom:s(t+"Bottom"+o,n)}}function D(e,t,n){e.style[p(t)]=n}function F(e){var t=e.parentElement,n=window.getComputedStyle(e),o=window.getComputedStyle(t),r=e.getBoundingClientRect(),a=t.getBoundingClientRect();return{elem:{style:n,width:r.width,height:r.height,top:r.top,bottom:r.bottom,left:r.left,right:r.right,margin:c(e,"margin",n),border:c(e,"border",n)},parent:{style:o,width:a.width,height:a.height,top:a.top,bottom:a.bottom,left:a.left,right:a.right,padding:c(t,"padding",o),border:c(t,"border",o)}}}var N=/^http:[\w\.\/]+svg$/;var R={animate:!1,cursor:"move",disablePan:!1,disableZoom:!1,disableXAxis:!1,disableYAxis:!1,duration:200,easing:"ease-in-out",exclude:[],excludeClass:"panzoom-exclude",handleStartEvent:function(e){e.preventDefault(),e.stopPropagation()},maxScale:4,minScale:.125,overflow:"hidden",panOnlyWhenZoomed:!1,relative:!1,setTransform:function(e,t,n){var o,r=t.x,a=t.y,i=t.scale;void 0===n&&(n={}),"boolean"==typeof n.animate&&(n.animate?(o=n,D(e,p("transition"),p("transform")+" "+o.duration+"ms "+o.easing)):D(e,"transition","none")),requestAnimationFrame(function(){D(e,"transform","scale("+i+") translate("+r+"px, "+a+"px)")})},startX:0,startY:0,startScale:1,step:.3};function e(g,w){if(!g)throw new Error("Panzoom requires an element as an argument");if(1!==g.nodeType)throw new Error("Panzoom requires an element with a nodeType of 1");if(t=(e=g).ownerDocument,n=e.parentElement,!(t&&n&&9===t.nodeType&&1===n.nodeType&&t.contains(n)))throw new Error("Panzoom should be called on elements that have been attached to the DOM");var e,t,n;w=T(T({},R),w);var o,s=(o=g,N.test(o.namespaceURI)&&"svg"!==o.nodeName.toLowerCase()),r=g.parentElement;r.style.overflow=w.overflow,r.style.userSelect="none",r.style.touchAction="none",g.style.cursor=w.cursor,g.style.userSelect="none",g.style.touchAction="none",D(g,"transformOrigin","string"==typeof w.origin?w.origin:s?"0 0":"50% 50%");var a,i,l,p,c,d,y=0,b=0,x=1,u=!1;function f(e,t,n){if(!n.silent){var o=new CustomEvent(e,{detail:t});g.dispatchEvent(o)}}function m(e,t){var n={x:y,y:b,scale:x};return t.setTransform(g,n,t),f(e,n,t),f("panzoomchange",n,t),n}function h(){if(w.contain){var e=F(g),t=e.parent.width-e.parent.border.left-e.parent.border.right,n=e.parent.height-e.parent.border.top-e.parent.border.bottom,o=t/(e.elem.width/x),r=n/(e.elem.height/x);"inside"===w.contain?w.maxScale=Math.min(o,r):"outside"===w.contain&&(w.minScale=Math.max(o,r))}}function v(e,t,n,o){var r=T(T({},w),o),a={x:y,y:b,opts:r};if(!r.force&&(r.disablePan||r.panOnlyWhenZoomed&&x===r.startScale))return a;if(e=parseFloat(e),t=parseFloat(t),r.disableXAxis||(a.x=(r.relative?y:0)+e),r.disableYAxis||(a.y=(r.relative?b:0)+t),"inside"===r.contain){var i=F(g);a.x=Math.max(-i.elem.margin.left-i.parent.padding.left,Math.min(i.parent.width-i.elem.width/n-i.parent.padding.left-i.elem.margin.left-i.parent.border.left-i.parent.border.right,a.x)),a.y=Math.max(-i.elem.margin.top-i.parent.padding.top,Math.min(i.parent.height-i.elem.height/n-i.parent.padding.top-i.elem.margin.top-i.parent.border.top-i.parent.border.bottom,a.y))}else if("outside"===r.contain){var l=(i=F(g)).elem.width/x,p=i.elem.height/x,s=l*n,c=p*n,d=(s-l)/2,u=(c-p)/2,f=(-(s-i.parent.width)-i.parent.padding.left-i.parent.border.left-i.parent.border.right+d)/n,m=(d-i.parent.padding.left)/n;a.x=Math.max(Math.min(a.x,m),f);var h=(-(c-i.parent.height)-i.parent.padding.top-i.parent.border.top-i.parent.border.bottom+u)/n,v=(u-i.parent.padding.top)/n;a.y=Math.max(Math.min(a.y,v),h)}return a}function E(e,t){var n=T(T({},w),t),o={scale:x,opts:n};return!n.force&&n.disableZoom||(o.scale=Math.min(Math.max(e,n.minScale),n.maxScale)),o}function S(e,t,n){var o=v(e,t,x,n),r=o.opts;return y=o.x,b=o.y,m("panzoompan",r)}function M(e,t){var n=E(e,t),o=n.opts;if(o.force||!o.disableZoom){e=n.scale;var r=y,a=b;if(o.focal){var i=o.focal;r=(i.x/e-i.x/x+y*e)/e,a=(i.y/e-i.y/x+b*e)/e}var l=v(r,a,e,{relative:!1,force:!0});return y=l.x,b=l.y,x=e,m("panzoomzoom",o)}}function O(e,t){var n=T(T(T({},w),{animate:!0}),t);return M(x*Math.exp((e?1:-1)*n.step),n)}function P(e,t,n){var o=F(g),r=o.parent.width-o.parent.padding.left-o.parent.padding.right-o.parent.border.left-o.parent.border.right,a=o.parent.height-o.parent.padding.top-o.parent.padding.bottom-o.parent.border.top-o.parent.border.bottom,i=t.clientX-o.parent.left-o.parent.padding.left-o.parent.border.left-o.elem.margin.left,l=t.clientY-o.parent.top-o.parent.padding.top-o.parent.border.top-o.elem.margin.top;s||(i-=o.elem.width/x/2,l-=o.elem.height/x/2);var p={x:i/r*(r*e),y:l/a*(a*e)};return M(e,T(T({},n),{focal:p,animate:!1}))}M(w.startScale,{animate:!1}),setTimeout(function(){h(),S(w.startX,w.startY,{animate:!1})});var z=[];function X(e){if(!function(e,t){for(var n=e;null!=n;n=n.parentElement)if(n.classList.contains(t.excludeClass)||-1<t.exclude.indexOf(n))return!0;return!1}(e.target,w)){L(z,e),u=!0,w.handleStartEvent(e),f("panzoomstart",{x:a=y,y:i=b,scale:x},w);var t=I(z);l=t.clientX,p=t.clientY,c=x,d=W(z)}}function Y(e){if(u&&void 0!==a&&void 0!==i&&void 0!==l&&void 0!==p){L(z,e);var t=I(z);if(1<z.length)P(E((W(z)-d)*w.step/80+c).scale,t);S(a+(t.clientX-l)/x,i+(t.clientY-p)/x,{animate:!1})}}function C(e){1===z.length&&f("panzoomend",{x:y,y:b,scale:x},w),function(e,t){if(t.touches)for(;e.length;)e.pop();else{var n=A(e,t);-1<n&&e.splice(n,1)}}(z,e),u&&(u=!1,a=i=l=p=void 0)}return w.disablePan||(Z("down",g,X),Z("move",document,Y,{passive:!0}),Z("up",document,C,{passive:!0})),{destroy:function(){q("down",g,X),q("move",document,Y),q("up",document,C)},getPan:function(){return{x:y,y:b}},getScale:function(){return x},getOptions:function(){return function(e){var t={};for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}(w)},pan:S,reset:function(e){var t=T(T(T({},w),{animate:!0}),e);x=E(t.startScale,t).scale;var n=v(t.startX,t.startY,x,t);return y=n.x,b=n.y,m("panzoomreset",t)},setOptions:function(e){for(var t in void 0===e&&(e={}),e)e.hasOwnProperty(t)&&(w[t]=e[t]);e.hasOwnProperty("cursor")&&(g.style.cursor=e.cursor),e.hasOwnProperty("overflow")&&(r.style.overflow=e.overflow),(e.hasOwnProperty("minScale")||e.hasOwnProperty("maxScale")||e.hasOwnProperty("contain"))&&h()},setStyle:function(e,t){return D(g,e,t)},zoom:M,zoomIn:function(e){return O(!0,e)},zoomOut:function(e){return O(!1,e)},zoomToPoint:P,zoomWithWheel:function(e,t){e.preventDefault();var n=T(T({},w),t),o=(0===e.deltaY&&e.deltaX?e.deltaX:e.deltaY)<0?1:-1;return P(E(x*Math.exp(o*n.step/3),n).scale,e,n)}}}return e.defaultOptions=R,e});