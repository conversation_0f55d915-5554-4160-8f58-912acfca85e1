<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PageTalk 划词助手测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-section h2 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .test-text {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4674ff;
            margin: 10px 0;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            overflow-x: auto;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>🔍 PageTalk 划词助手测试页面</h1>
    
    <div class="instructions">
        <h3>📋 测试说明</h3>
        <p>请在以下文本中选择任意内容，测试划词助手的各项功能：</p>
        <ul>
            <li><strong>解读功能</strong>：选择复杂概念或专业术语</li>
            <li><strong>翻译功能</strong>：选择中文或英文文本</li>
            <li><strong>对话功能</strong>：选择任意文本进行深度讨论</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🧠 人工智能与机器学习</h2>
        <div class="test-text">
            人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。
        </div>
        
        <div class="test-text">
            Machine learning is a subset of artificial intelligence that focuses on the development of algorithms and statistical models that enable computer systems to improve their performance on a specific task through experience, without being explicitly programmed.
        </div>
    </div>

    <div class="test-section">
        <h2>💻 编程代码示例</h2>
        <div class="code-block">
function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}

// 计算斐波那契数列的第10项
console.log(fibonacci(10)); // 输出: 55
        </div>
        
        <div class="test-text">
            上述代码实现了经典的斐波那契数列算法。斐波那契数列是一个数学序列，其中每个数字都是前两个数字的和。这个递归实现虽然简洁，但在计算大数时效率较低，因为存在重复计算的问题。
        </div>
    </div>

    <div class="test-section">
        <h2>🌍 科学与技术</h2>
        <div class="test-text">
            量子计算是一种遵循量子力学规律调控量子信息单元进行计算的新型计算模式。相对于传统的通用计算机，其理论模型是通用图灵机；通用的量子计算机，其理论模型是用量子力学规律重新诠释的通用图灵机。
        </div>
        
        <div class="test-text">
            Quantum computing harnesses the phenomena of quantum mechanics to deliver a huge leap forward in computation to solve certain problems. Unlike classical computers that use bits (0 or 1), quantum computers use quantum bits or qubits, which can exist in multiple states simultaneously through superposition.
        </div>
    </div>

    <div class="test-section">
        <h2>📊 数学公式</h2>
        <div class="test-text">
            爱因斯坦的质能方程 E=mc² 表明质量和能量之间存在等价关系。其中 E 代表能量，m 代表质量，c 代表光速。这个方程揭示了物质可以转化为能量，是核能和核武器的理论基础。
        </div>
        
        <div class="test-text">
            The Pythagorean theorem states that in a right triangle, the square of the hypotenuse (the side opposite the right angle) is equal to the sum of the squares of the other two sides. This can be written as: a² + b² = c².
        </div>
    </div>

    <div class="test-section">
        <h2>🎨 文学与艺术</h2>
        <div class="test-text">
            "生活不是缺少美，而是缺少发现美的眼睛。" 这句话出自法国雕塑家罗丹，强调了观察力和审美能力在生活中的重要性。艺术不仅存在于博物馆和画廊中，更存在于我们日常生活的每一个细节里。
        </div>
        
        <div class="test-text">
            Renaissance art represents a cultural movement that profoundly affected European intellectual life in the early modern period. Beginning in Italy, and spreading to the rest of Europe by the 16th century, its influence was felt in art, architecture, philosophy, literature, music, science, and politics.
        </div>
    </div>

    <div class="test-section">
        <h2>🔬 复杂概念测试</h2>
        <div class="test-text">
            区块链是一个分布式数据库，即由多个节点共同维护的数据库。它采用密码学的方法来保证已有数据不可能被篡改。区块链系统由数据层、网络层、共识层、激励层、合约层和应用层组成。
        </div>
        
        <div class="test-text">
            CRISPR-Cas9 is a revolutionary gene-editing technology that allows scientists to make precise changes to DNA. The system consists of two key molecules: a guide RNA (gRNA) that contains a sequence that binds to the target DNA, and the Cas9 enzyme that acts as molecular scissors to cut the DNA at the precise location.
        </div>
    </div>

    <script>
        // 简单的页面交互
        document.addEventListener('DOMContentLoaded', function() {
            console.log('PageTalk 测试页面已加载');
            
            // 添加选择文本的提示
            document.addEventListener('mouseup', function() {
                const selection = window.getSelection();
                if (selection.toString().trim().length > 0) {
                    console.log('选中文本:', selection.toString().trim());
                }
            });
        });
    </script>
</body>
</html>
