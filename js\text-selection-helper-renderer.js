/**
 * PageTalk - Text Selection Helper Renderer Module
 * 划词助手渲染工具模块
 * 
 * 统一处理 Markdown 渲染和图标渲染逻辑
 */

/**
 * SVG 图标定义（作为后备方案）
 */
const FALLBACK_SVG_ICONS = {
    interpret: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
        <circle cx="12" cy="12" r="3"/>
    </svg>`,
    translate: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
        <path d="M5 8l6 6"/>
        <path d="M4 14l6-6 2-3"/>
        <path d="M2 5h12"/>
        <path d="M7 2h1"/>
        <path d="M22 22l-5-10-5 10"/>
        <path d="M14 18h6"/>
    </svg>`,
    chat: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
    </svg>`,
    custom: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
        <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
    </svg>`
};

/**
 * Lucide 图标别名映射
 */
const LUCIDE_ICON_ALIASES = {
    'stop': 'CircleStop',
    'shop': 'ShoppingBag',
    'bank': 'Banknote',
    'scanner': 'Scan'
};

/**
 * 渲染器类
 */
class TextSelectionHelperRenderer {
    /**
     * 渲染 Markdown 内容
     * @param {string} text - 要渲染的文本
     * @returns {string} 渲染后的 HTML
     */
    static renderMarkdown(text) {
        if (!text || typeof text !== 'string') {
            return '';
        }

        // 优先使用主面板的 MarkdownRenderer
        if (window.MarkdownRenderer && typeof window.MarkdownRenderer.render === 'function') {
            try {
                return window.MarkdownRenderer.render(text);
            } catch (error) {
                console.warn('[TextSelectionHelperRenderer] MarkdownRenderer failed:', error);
            }
        }

        // 备用方案：简单的 HTML 转义
        return `<p>${this.escapeHtml(text)}</p>`;
    }

    /**
     * 渲染 Lucide 图标
     * @param {string} iconName - 图标名称
     * @param {number} size - 图标大小，默认 16
     * @param {string} className - 额外的 CSS 类名
     * @returns {string} SVG 图标的 HTML 字符串
     */
    static renderLucideIcon(iconName, size = 16, className = '') {
        try {
            // 检查 Lucide 是否可用
            if (typeof lucide === 'undefined') {
                console.warn('[TextSelectionHelperRenderer] Lucide library not available, using fallback');
                return this.getFallbackIcon(iconName, size);
            }

            // 转换为 PascalCase
            const pascalCaseName = iconName.charAt(0).toUpperCase() + iconName.slice(1);
            
            // 检查图标是否存在
            if (!lucide[pascalCaseName]) {
                // 尝试别名映射
                const aliasName = LUCIDE_ICON_ALIASES[iconName];
                if (aliasName && lucide[aliasName]) {
                    return lucide[aliasName].toSvg({ 
                        width: size, 
                        height: size, 
                        class: className 
                    });
                }
                
                console.warn(`[TextSelectionHelperRenderer] Lucide icon "${iconName}" not found, using fallback`);
                return this.getFallbackIcon(iconName, size);
            }

            return lucide[pascalCaseName].toSvg({ 
                width: size, 
                height: size, 
                class: className 
            });
        } catch (error) {
            console.error('[TextSelectionHelperRenderer] Error rendering Lucide icon:', error);
            return this.getFallbackIcon(iconName, size);
        }
    }

    /**
     * 获取选项图标
     * @param {string} optionId - 选项 ID
     * @param {string} customIcon - 自定义图标名称
     * @returns {string} SVG 图标的 HTML 字符串
     */
    static getOptionIcon(optionId, customIcon = null) {
        // 如果有自定义图标，使用 Lucide 渲染
        if (customIcon) {
            return this.renderLucideIcon(customIcon, 16);
        }

        // 使用默认图标
        return this.getFallbackIcon(optionId, 16);
    }

    /**
     * 获取后备图标
     * @param {string} iconName - 图标名称
     * @param {number} size - 图标大小
     * @returns {string} SVG 图标的 HTML 字符串
     */
    static getFallbackIcon(iconName, size = 16) {
        const icon = FALLBACK_SVG_ICONS[iconName] || FALLBACK_SVG_ICONS.custom;
        return icon.replace(/width="16"/g, `width="${size}"`).replace(/height="16"/g, `height="${size}"`);
    }

    /**
     * HTML 转义函数
     * @param {string} text - 要转义的文本
     * @returns {string} 转义后的文本
     */
    static escapeHtml(text) {
        if (!text || typeof text !== 'string') {
            return '';
        }

        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    /**
     * 渲染动态内容（KaTeX、Mermaid 等）
     * @param {HTMLElement} element - 要渲染的元素
     * @param {boolean} isStreaming - 是否正在流式输出中
     */
    static renderDynamicContent(element, isStreaming = false) {
        if (!element) return;

        // 渲染 KaTeX
        this.renderKaTeX(element);

        // 渲染 Mermaid（仅在非流式输出或输出完成时）
        if (!isStreaming) {
            this.renderMermaid(element);
        }

        // 为代码块添加复制按钮
        this.addCopyButtonsToCodeBlocks(element);
    }

    /**
     * 渲染 KaTeX 数学公式
     * @param {HTMLElement} element - 要渲染的元素
     */
    static renderKaTeX(element) {
        if (typeof window.renderMathInElement === 'function') {
            try {
                window.renderMathInElement(element, {
                    delimiters: [
                        {left: "$$", right: "$$", display: true},
                        {left: "\\[", right: "\\]", display: true},
                        {left: "$", right: "$", display: false},
                        {left: "\\(", right: "\\)", display: false}
                    ],
                    throwOnError: false
                });
            } catch (error) {
                console.warn('[TextSelectionHelperRenderer] KaTeX rendering failed:', error);
            }
        }
    }

    /**
     * 渲染 Mermaid 图表
     * @param {HTMLElement} element - 要渲染的元素
     */
    static renderMermaid(element) {
        if (typeof mermaid === 'undefined' || !window.textSelectionHelperMermaidInitialized) {
            return;
        }

        const mermaidBlocks = element.querySelectorAll('pre code.language-mermaid');
        mermaidBlocks.forEach((block, index) => {
            const definition = block.textContent.trim();
            
            if (!this.isMermaidDefinitionComplete(definition)) {
                return;
            }

            const renderId = `mermaid-selection-${Date.now()}-${index}`;
            const container = document.createElement('div');
            container.className = 'mermaid';
            container.id = `${renderId}-container`;
            container.dataset.mermaidDefinition = definition;

            try {
                mermaid.render(renderId, definition).then(({ svg }) => {
                    container.innerHTML = svg;
                    container.style.cursor = 'pointer';
                    container.addEventListener('click', () => {
                        if (window.showMermaidModal) {
                            window.showMermaidModal(svg);
                        }
                    });
                }).catch(error => {
                    console.warn('[TextSelectionHelperRenderer] Mermaid render failed:', error);
                });

                block.parentNode.replaceWith(container);
            } catch (error) {
                console.warn('[TextSelectionHelperRenderer] Mermaid processing failed:', error);
            }
        });
    }

    /**
     * 检查 Mermaid 定义是否完整
     * @param {string} definition - Mermaid 定义
     * @returns {boolean} 是否完整
     */
    static isMermaidDefinitionComplete(definition) {
        const trimmed = definition.trim();
        if (!trimmed) return false;

        // 基本完整性检查
        const lines = trimmed.split('\n').filter(line => line.trim());
        return lines.length >= 2; // 至少需要图表类型和一个节点/连接
    }

    /**
     * 为代码块添加复制按钮
     * @param {HTMLElement} element - 要处理的元素
     */
    static addCopyButtonsToCodeBlocks(element) {
        const codeBlocks = element.querySelectorAll('pre code');
        codeBlocks.forEach(codeBlock => {
            this.addCopyButtonToCodeBlock(codeBlock);
        });
    }

    /**
     * 为单个代码块添加复制按钮
     * @param {HTMLElement} codeBlock - 代码块元素
     */
    static addCopyButtonToCodeBlock(codeBlock) {
        // 检查是否已经有复制按钮
        if (codeBlock.querySelector('.copy-button')) {
            return;
        }

        const pre = codeBlock.closest('pre');
        if (!pre) return;

        // 创建复制按钮
        const copyButton = document.createElement('button');
        copyButton.className = 'copy-button';
        copyButton.innerHTML = `
            <svg width="14" height="14" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
            </svg>
        `;
        copyButton.title = '复制代码';

        // 添加点击事件
        copyButton.addEventListener('click', async () => {
            try {
                await navigator.clipboard.writeText(codeBlock.textContent);
                copyButton.innerHTML = `
                    <svg width="14" height="14" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                        <polyline points="20,6 9,17 4,12"/>
                    </svg>
                `;
                setTimeout(() => {
                    copyButton.innerHTML = `
                        <svg width="14" height="14" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                        </svg>
                    `;
                }, 2000);
            } catch (error) {
                console.error('[TextSelectionHelperRenderer] Copy failed:', error);
            }
        });

        // 添加到 pre 元素
        pre.style.position = 'relative';
        pre.appendChild(copyButton);
    }
}

// 导出到全局
window.TextSelectionHelperRenderer = TextSelectionHelperRenderer;

console.log('[TextSelectionHelperRenderer] Renderer module loaded');
