{"manifest_version": 3, "name": "PageTalk - Your Gemini Browser Q&A Assistant", "version": "3.5.0", "description": "A browser extension that can read page content and use the Gemini API for Q&A", "permissions": ["activeTab", "storage", "scripting", "contextMenus", "proxy"], "host_permissions": ["<all_urls>"], "action": {"default_title": "PageTalk", "default_icon": "magic.png"}, "icons": {"128": "magic.png"}, "background": {"service_worker": "js/background.js", "type": "module"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["js/lib/Readability.js", "js/lib/markdown-it.min.js", "js/lib/highlight.min.js", "js/lib/python.min.js", "js/lib/r.min.js", "js/lib/sql.min.js", "js/lib/json.min.js", "js/lib/katex.min.js", "js/lib/auto-render.min.js", "js/lib/mhchem.min.js", "js/lib/mermaid.min.js", "js/lib/panzoom.min.js", "js/lib/lucide.js", "js/translations.js", "js/markdown-renderer.js", "js/text-selection-helper-config.js", "js/text-selection-helper-renderer.js", "js/text-selection-helper.js", "js/content.js"], "css": ["css/content-panel.css", "css/text-selection-helper.css", "css/github.min.css", "css/github-dark-dimmed.min.css", "css/katex.min.css"], "run_at": "document_start"}], "web_accessible_resources": [{"resources": ["html/*", "css/*", "js/*", "js/lib/*", "magic.png"], "matches": ["<all_urls>"]}], "commands": {"_execute_action": {"suggested_key": {"default": "Alt+P", "mac": "Alt+P"}, "description": "打开 Pagetalk 面板"}}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}}