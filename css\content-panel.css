/* Pagetalk 面板样式 */
#pagetalk-panel-container {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  height: 100vh !important;
  /* width 由 JS 控制并从 localStorage 读取 */
  z-index: 2147483647 !important;
  display: none;
  /* 旧的: box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1); */
  /* 新的: 使用多重阴影增加层次感和柔和的边缘 */
  /* 第一层是一个非常细微的、几乎不可见的"边框"式阴影，用于清晰边界 */
  /* 第二层是一个向左的、更柔和的投射阴影，用于增加深度 */
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.04), /* 极细的轮廓阴影 */
    -4px 0 12px rgba(0, 0, 0, 0.08); /* 更柔和的左侧投射阴影 */
  background-color: white; /* 确保 iframe 容器本身有背景色，以便阴影正确显示 */
}

#pagetalk-panel-resizer {
  position: absolute;
  left: -4px; /* 让一部分resizer在面板外部，更容易拖动 */
  top: 0;
  width: 8px; /* 增加可点击区域 */
  height: 100%;
  cursor: ew-resize;
  z-index: 10; /* 确保 resizer 在 iframe 之上，但在可能的其他插件模态框之下 */
  /* background-color: rgba(0,0,0,0.01); */ /* 可选：给resizer一点视觉提示，但要确保不影响透明度 */
}

#pagetalk-panel-iframe {
  width: 100% !important;
  height: 100% !important;
  border: none;
}

/* 当面板打开时，给页面添加右侧边距 */
body.pagetalk-panel-open {
  /* margin-right 由 JS动态设置 */
  transition: margin-right 0.2s ease-out;
}

/* 新增：拖动时禁用页面其他元素事件的样式 */
body.pagetalk-resizing-active {
  cursor: ew-resize !important;
  user-select: none !important;
  -webkit-user-select: none !important; /* Safari and Chrome */
  -moz-user-select: none !important;    /* Firefox */
  -ms-user-select: none !important;     /* IE10+ */
}

body.pagetalk-resizing-active > *:not(#pagetalk-panel-container) {
    pointer-events: none !important;
}

/* 针对PDF阅读器调整的辅助类 */
.pagetalk-adjusted {
    transition: margin-right 0.2s ease-out, width 0.2s ease-out;
}
