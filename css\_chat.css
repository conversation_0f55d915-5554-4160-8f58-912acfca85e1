/* 包含 Pagetalk 聊天界面的所有样式，如消息气泡、输入区域、图片预览、思考动画等。 */

/* 聊天界面样式 */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    overflow: hidden; /* 确保聊天容器本身不溢出 */
    padding-bottom: 0; /* 移除原有的，由 chat-input 和 selected-tabs-bar 控制 */
}

/* 聊天消息区域 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: calc(var(--spacing-md) + 8px); /* 进一步增大间距 */
    width: 100%;
    min-height: 0; /* 确保可以缩小 */
}

/* 欢迎消息 */
.welcome-message {
    background-color: var(--card-background);
    border-radius: var(--radius-lg);
    padding: clamp(var(--spacing-sm), 2vw, var(--spacing-md)); /* 缩小内边距 */
    margin: var(--spacing-md) auto; /* 缩小外边距 */
    width: clamp(180px, 90%, 35%); /* 缩小宽度 */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
    text-align: center;
    border: 1px solid var(--border-color);
    /* 添加入场动画 */
    animation: welcomeSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    opacity: 0;
    transform: translateY(20px) scale(0.95);
}

@keyframes welcomeSlideIn {
    0% {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.welcome-message h2 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs); /* 缩小边距 */
    font-size: clamp(14px, 3.5vw, 16px); /* 缩小字体 */
    font-weight: 600;
    letter-spacing: -0.5px;
}

.welcome-message p {
    margin-bottom: var(--spacing-xs); /* 缩小边距 */
    font-size: clamp(11px, 3vw, 13px); /* 缩小字体 */
    line-height: 1.5; /* 稍微减小行高 */
    color: var(--text-secondary);
}

/* 快捷操作按钮样式 */
.quick-actions {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: clamp(var(--spacing-xs), 2vw, var(--spacing-md));
    margin-top: var(--spacing-md);
}

.quick-action-btn {
    background-color: rgba(49, 123, 245, 0.08);
    border: none;
    border-radius: var(--radius-full);
    padding: var(--spacing-xs) clamp(var(--spacing-xs), 3vw, var(--spacing-md));
    font-size: clamp(11px, 3vw, 13px);
    color: var(--primary-color);
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    white-space: nowrap;
    transform: scale(1);
    position: relative;
    overflow: hidden;
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(49, 123, 245, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.quick-action-btn:hover {
    background-color: rgba(49, 123, 245, 0.15);
    transform: scale(1.02);
    box-shadow: 0 2px 8px rgba(49, 123, 245, 0.15);
}

.quick-action-btn:hover::before {
    width: 100%;
    height: 100%;
}

.quick-action-btn:active {
    transform: scale(0.98);
}

/* 消息气泡样式优化 */
.message {
    padding: 0; /* Base padding is 0, content inside will have its own */
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-md); /* Standard bottom margin */
    word-wrap: break-word;
    overflow-wrap: break-word;
    position: relative;
    max-width: 90%;
    width: fit-content; /* Allows bubble to shrink to content width */
    box-sizing: border-box; /* Important for max-width and padding */
    /* 默认状态 - 为动画做准备 */
    opacity: 0;
    transform: translateY(8px) scale(0.98);
    animation: messageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* 消息入场动画 - 更丝滑的缓动曲线 */
@keyframes messageSlideIn {
    0% {
        opacity: 0;
        transform: translateY(8px) scale(0.98);
    }
    60% {
        opacity: 0.8;
        transform: translateY(-1px) scale(1.01);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 用户消息特殊入场动画 - 从右侧滑入 */
.user-message {
    animation: userMessageSlideIn 0.35s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes userMessageSlideIn {
    0% {
        opacity: 0;
        transform: translateX(12px) translateY(4px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateX(0) translateY(0) scale(1);
    }
}

/* 机器人消息特殊入场动画 - 从左侧滑入 */
.bot-message {
    animation: botMessageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes botMessageSlideIn {
    0% {
        opacity: 0;
        transform: translateX(-8px) translateY(6px) scale(0.96);
    }
    100% {
        opacity: 1;
        transform: translateX(0) translateY(0) scale(1);
    }
}

/* 用户消息气泡样式 */
.user-message {
    align-self: flex-end;
    background-color: var(--primary-color);
    color: white;
    /* border-radius already set by .message */
    display: inline-block; /* For width: auto to work with align-self */
    max-width: 95%; /* Can be slightly wider than bot messages */
    width: auto; /* Content dictates width, up to max-width */
    overflow: visible; /* Ensure internal content like images can layout */
    padding: var(--spacing-sm) var(--spacing-md); /* Standard internal padding */
    text-align: left;
    position: relative; /* For message actions */
    /* 微妙的悬停效果 */
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.2s ease;
    /* 新增：为 sent-tabs-container 腾出空间，如果它直接在此元素上方的话 */
    /* 但由于它们是兄弟元素，间距由 .chat-messages 的 gap 或 .sent-tabs-container 的 margin-bottom 控制 */
}

.user-message:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(49, 123, 245, 0.15);
}

/* 用户消息气泡内已发送标签页的容器 - 现在是外部容器的样式 */
.sent-tabs-container {
    display: flex;
    flex-direction: column;
    align-self: flex-end; /* 与用户消息气泡对齐 */
    gap: calc(var(--spacing-xs) / 2);
    margin-bottom: calc(var(--spacing-xs) / 2); /* 与下方用户消息气泡的间距 */
    /* padding-bottom: calc(var(--spacing-xs) / 2); */ /* 不再需要，因为不再是内部容器 */
    /* border-bottom: 1px solid rgba(255, 255, 255, 0.2); */ /* 不再需要，因为不再是内部容器 */
    width: fit-content; /* 根据内容调整宽度 */
    max-width: 90%; /* 与用户消息气泡最大宽度相似或略小 */
    margin-right: 0; /* 确保与 flex-end 对齐 */
    /* 为了美观，可以给这个容器也加上一点圆角和背景，使其看起来像一组独立的标签 */
    /* background-color: rgba(0,0,0,0.03); /* 非常淡的背景 */
    /* padding: var(--spacing-xs); */
    /* border-radius: var(--radius-md); */
}

.sent-tab-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: calc(var(--spacing-xs) / 1.2) var(--spacing-sm); /* 调整内边距使标签更高 */
    padding-left: calc(var(--spacing-sm) + 2px); /* 增加左侧内边距，让图标不靠近边缘 */
    border-radius: var(--radius-md);
    background-color: var(--card-background-rgb-opacity-low);
    border: 1px solid var(--border-color);
    max-width: 100%;
    /* overflow: hidden; */ /* Removed to let flex children handle overflow */
    box-shadow: var(--shadow-sm);
    color: var(--text-secondary);
}

.sent-tab-favicon {
    width: 16px; /* 增加图标大小 */
    height: 16px; /* 增加图标大小 */
    object-fit: contain;
    flex-shrink: 0;
    border-radius: 2px;
}

.sent-tab-title {
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    /* color: rgba(255, 255, 255, 0.9); */ /* 改为继承或使用变量 */
    /* max-width: calc(100% - 30px); */ /* Removed fixed max-width */
    flex-grow: 1; /* Allow title to take available space */
    flex-shrink: 1; /* Allow title to shrink */
    min-width: 0; /* Critical for ellipsis to work in flex item */
    position: relative;
}

/* "Remove Sent Tab" button in user message bubble */
.remove-sent-tab-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 16px; /* Larger for easier clicking */
    font-weight: bold;
    padding: 0 4px;
    margin-left: var(--spacing-xs);
    cursor: pointer;
    line-height: 1;
    opacity: 0.6;
    transition: all 0.2s ease;
    align-self: center; /* Vertically center if tab item is taller */
    flex-shrink: 0; /* Ensure the button does not shrink */
}

.remove-sent-tab-btn:hover {
    color: var(--error-color);
    opacity: 1;
    transform: scale(1.1);
}

/* 可选：为 sent-tab-title 添加更平滑的渐变省略（如果纯 ellipsis 不够） */
/*
.sent-tab-title::after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 20px;
    background: linear-gradient(to right, transparent, var(--primary-color) 80%);
}
.user-message:not(:hover) .sent-tab-title::after {
     background: linear-gradient(to right, transparent, var(--primary-color) 80%); 
}
*/

.user-message p {
    margin: 0;
    padding: 0;
    line-height: 1.4;
}

/* 机器人消息气泡样式 */
.bot-message {
    align-self: flex-start;
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    /* border-radius already set by .message */
    padding: var(--spacing-md); /* Standard internal padding */
    padding-bottom: 30px; /* Space for actions at the bottom */
    max-width: 95%;
    overflow: visible; /* Ensure markdown content can layout (e.g. tables) */
    line-height: 1.4;
    position: relative; /* For message actions */
    /* 微妙的悬停效果 */
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.2s ease, border-color 0.2s ease;
}

.bot-message:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border-color: rgba(49, 123, 245, 0.2);
}

.bot-message p {
    margin-top: 0;
    margin-bottom: 0.6em; /* Spacing between paragraphs in bot message */
    line-height: 1.4;
}

.bot-message p:last-child {
    margin-bottom: 0;
}

.bot-message p + p::before { /* Remove extra space if any was added by browser */
    content: none;
    display: none;
    height: 0;
}

/* 消息操作按钮容器 */
.message-actions {
    position: absolute;
    display: flex;
    gap: 4px; /* Reduced gap */
    opacity: 0; /* Default hidden */
    transform: translateY(2px) scale(0.9);
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 5;
}

/* 用户消息中按钮位置调整到气泡下方 */
.user-message .message-actions {
    bottom: -24px;
    right: 0;
}

/* 机器人消息中按钮位置保持在气泡内右下角 */
.bot-message .message-actions {
    bottom: var(--spacing-xs);
    right: var(--spacing-xs);
}

/* 消息悬停时显示操作按钮 */
.message:hover .message-actions,
.message-actions:hover { /* 保持原有样式 */
    opacity: 1;
    transform: translateY(2px) scale(1); /* Modified to remove upward lift */
}

/* 通用消息操作按钮样式 (复制、删除、重新生成) */
.message-action-btn {
    width: 24px;
    height: 24px;
    border-radius: var(--radius-full);
    background-color: rgba(255, 255, 255, 0.9); /* 浅色模式背景 */
    border: none; /* 确保无边框 */
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 0;
    color: #888; /* 默认灰色 */
    backdrop-filter: blur(8px);
}
.message-action-btn:hover {
    transform: scale(1.1);
    color: var(--primary-color); /* Default hover to primary color */
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.message-action-btn:active {
    transform: scale(0.95);
}

/* 用户消息内的按钮特定背景颜色 */
.user-message .message-action-btn {
    background-color: rgba(255, 255, 255, 0.3);
    color: rgba(104, 103, 103, 0.8);
}
.user-message .message-action-btn:hover {
    background-color: rgba(255, 255, 255, 0.6);
    color: var(--primary-color); /* User message action buttons also hover to primary */
}

/* 聊天消息中删除操作按钮的特定悬停样式 (现在与用户消息操作按钮悬停一致) */
.message-action-btn.delete-message-action:hover {
    background-color: rgba(255, 255, 255, 0.6); /* 白色背景，与用户消息按钮悬停一致 */
    color: var(--primary-color); /* 主题色图标，与用户消息按钮悬停一致 */
}

/* 复制按钮基础样式 - 使 message-action-btn 行为一致 */
.copy-button {
    width: 24px;
    height: 24px;
    border-radius: var(--radius-full);
    background-color: rgba(255, 255, 255, 0.9); /* 浅色模式背景 (与 message-action-btn 一致) */
    border: none; /* 非常重要：移除浏览器默认边框 */
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 0;
    color: #888; /* 默认灰色 (与 message-action-btn 一致) */
}
.copy-button:hover { /* 通用复制按钮悬停效果 */
    transform: scale(1.05);
    color: var(--primary-color);
    background-color: white; /* 稍微变亮一点的背景以示区别 */
}

/* Ensure copy button inside message actions is part of flex layout */
.message-actions .copy-button {
    position: static !important; 
    bottom: auto;     
    right: auto;      
    /* 继承上述 .copy-button 的基础样式 */
}

/* 用户消息中的复制按钮特定样式 (覆盖基础 .copy-button 样式) */
.user-message .copy-button {
    background-color: rgba(255, 255, 255, 0.3); /* 用户消息气泡中的按钮背景 */
    color: rgba(104, 103, 103, 0.8); /* 用户消息气泡中的按钮颜色 */
}
.user-message .copy-button:hover {
    background-color: rgba(255, 255, 255, 0.6); /* 用户消息气泡中按钮悬停背景 */
    color: var(--primary-color); /* 保持悬停颜色一致 */
}


/* 流式输出光标样式调整 */
.streaming-cursor {
    display: inline-block;
    width: 2px;
    height: 1em;
    background-color: var(--primary-color);
    margin-left: 2px;
    animation: streamingPulse 1.2s infinite cubic-bezier(0.4, 0, 0.6, 1);
    vertical-align: text-bottom;
    border-radius: 1px;
}

@keyframes streamingPulse {
    0%, 100% {
        opacity: 1;
        transform: scaleY(1);
    }
    50% {
        opacity: 0.4;
        transform: scaleY(0.8);
    }
}


/* AI思考动画样式 - 更高级的设计 */
.thinking {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
    min-height: 40px;
    /* 添加思考状态的入场动画 - 只播放一次后保持显示 */
    animation: thinkingAppear 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    position: relative;
    /* 确保初始状态是可见的 */
    opacity: 1;
    transform: translateY(0) scale(1);
}

@keyframes thinkingAppear {
    from {
        opacity: 0;
        transform: translateY(8px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.thinking-dots {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
}

/* 添加背景光晕效果 */
.thinking-dots::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 20px;
    background: radial-gradient(ellipse, rgba(49, 123, 245, 0.15) 0%, transparent 70%);
    border-radius: 50%;
    animation: thinkingGlow 2s infinite ease-in-out;
}

@keyframes thinkingGlow {
    0%, 100% {
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1.2);
    }
}

.thinking-dots span {
    display: inline-block;
    width: 7px;
    height: 7px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), rgba(49, 123, 245, 0.7));
    opacity: 0.4;
    position: relative;
    z-index: 1;
    box-shadow: 0 1px 3px rgba(49, 123, 245, 0.2);
}

.thinking-dots span:nth-child(1) {
    animation: thinkingPulseAdvanced 1.6s infinite cubic-bezier(0.4, 0, 0.6, 1);
    animation-delay: 0s;
}

.thinking-dots span:nth-child(2) {
    animation: thinkingPulseAdvanced 1.6s infinite cubic-bezier(0.4, 0, 0.6, 1);
    animation-delay: 0.2s;
}

.thinking-dots span:nth-child(3) {
    animation: thinkingPulseAdvanced 1.6s infinite cubic-bezier(0.4, 0, 0.6, 1);
    animation-delay: 0.4s;
}

@keyframes thinkingPulseAdvanced {
    0%, 60%, 100% {
        transform: scale(0.8) translateY(0);
        opacity: 0.4;
        box-shadow: 0 1px 3px rgba(49, 123, 245, 0.2);
    }
    30% {
        transform: scale(1.3) translateY(-2px);
        opacity: 1;
        box-shadow: 0 3px 8px rgba(49, 123, 245, 0.4);
    }
}

/* 添加额外的微粒效果 */
.thinking-dots span::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: inherit;
    opacity: 0;
    animation: thinkingRipple 1.6s infinite cubic-bezier(0.4, 0, 0.6, 1);
}

.thinking-dots span:nth-child(1)::after {
    animation-delay: 0s;
}

.thinking-dots span:nth-child(2)::after {
    animation-delay: 0.2s;
}

.thinking-dots span:nth-child(3)::after {
    animation-delay: 0.4s;
}

@keyframes thinkingRipple {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.6;
    }
    100% {
        transform: translate(-50%, -50%) scale(2.5);
        opacity: 0;
    }
}

/* 聊天输入区域 */
.chat-input {
    display: flex;
    align-items: center;
    padding: clamp(var(--spacing-xs), 1.5vw, var(--spacing-sm)) clamp(var(--spacing-xs), 2vw, var(--spacing-md));
    /* background-color: var(--card-background); /* 移动到父级 div.chat-input-area */
    /* border-top: 1px solid var(--border-color); /* 移动到父级 div.chat-input-area */
    width: 100%;
    min-height: fit-content;
    box-sizing: border-box;
    overflow-x: hidden;
    background-color: var(--card-background); /* 确保 chat-input 本身有背景 */
    border-top: 1px solid var(--border-color); /* 如果 selected-tabs-bar 不存在，这个边框会显示 */
    position: relative; /* 用于确保圆角在 selected-tabs-bar 出现时能正确处理 */
    border-radius: var(--radius-lg) var(--radius-lg) 0 0; /* 默认顶部圆角，底部直角 */
}

.chat-input textarea {
    flex: 1;
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 2px 14px; /* 减小垂直内边距 */
    resize: none;
    min-height: 20px; /* 减小默认高度 */
    max-height: 150px; /* 限定最大高度 */
    font-size: clamp(12px, 3.5vw, 14px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
    min-width: 0;
    overflow-y: auto;
    line-height: 1.4;
    /* 添加轻微的变换效果 */
    transform: translateY(0);
}

.chat-input textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(49, 123, 245, 0.1), 0 2px 8px rgba(49, 123, 245, 0.05);
    transform: translateY(-1px);
}

/* 输入时的微妙反馈 */
.chat-input textarea::placeholder {
    color: #999; /* 更浅的灰色 */
    opacity: 1; /* 确保在所有浏览器中都可见 */
}

.chat-input textarea:not(:placeholder-shown) {
    border-color: rgba(49, 123, 245, 0.3);
}

.chat-input textarea::-webkit-scrollbar {
    width: 6px;
}
.chat-input textarea::-webkit-scrollbar-track {
    background: transparent;
}
.chat-input textarea::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: var(--radius-full);
}
.chat-input textarea::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover);
}

/* 输入区域按钮 (上传、发送) */
.chat-input button {
    width: clamp(30px, 8vw, 34px);
    height: clamp(30px, 8vw, 34px);
    border-radius: 50%;
    border: none;
    background-color: transparent;
    margin-left: var(--spacing-xs);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    transform: scale(1);
    position: relative;
}

.chat-input button:not(#send-message):hover { /* Non-send buttons hover */
    color: var(--primary-color);
    background-color: var(--button-hover-bg);
    transform: scale(1.05);
}

.chat-input button:not(#send-message):active {
    transform: scale(0.95);
}

.chat-input button svg {
    width: clamp(14px, 4vw, 16px);
    height: clamp(14px, 4vw, 16px);
}

/* 发送按钮 */
#send-message {
    background-color: var(--primary-color);
    color: white;
    width: clamp(26px, 6.5vw, 30px);
    height: clamp(26px, 6.5vw, 30px);
    position: relative;
    overflow: hidden;
}

#send-message::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

#send-message:hover::before {
    width: 100%;
    height: 100%;
}

#send-message svg {
    width: clamp(13px, 3.5vw, 15px);
    height: clamp(13px, 3.5vw, 15px);
    position: relative;
    z-index: 1;
    transition: transform 0.2s ease;
}

#send-message:hover {
    background-color: var(--secondary-color);
    transform: scale(1.08);
    box-shadow: 0 2px 12px rgba(49, 123, 245, 0.3);
}

#send-message:hover svg {
    transform: translateX(1px);
}

#send-message:active {
    transform: scale(0.95);
}

/* 发送按钮点击时的脉冲效果 */
@keyframes sendPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(49, 123, 245, 0.4);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 8px rgba(49, 123, 245, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(49, 123, 245, 0);
    }
}

#send-message.sending {
    animation: sendPulse 0.6s ease-out;
}

/* 控制流式输出按钮特定样式 */
#send-message.stop-streaming {
    /* background remains primary, hover changes */
    transform: none; /* Reset hover transform if any conflicts */
}
#send-message.stop-streaming:hover {
    background-color: var(--error-color); /* Hover becomes red */
    color: white;
    transform: scale(1.05);
}


/* 图片预览容器 */
.image-preview-container {
    margin: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-md);
    background-color: var(--card-background);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    max-width: 100%;
    box-sizing: border-box;
}

/* 图片网格布局 */
.images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: var(--spacing-xs);
    padding: var(--spacing-xs);
}

.image-item {
    position: relative;
    height: 100px;
    border-radius: var(--radius-sm);
    overflow: hidden;
}
.image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}
.image-item .image-actions {
    position: absolute;
    top: var(--spacing-xs);
    right: var (--spacing-xs);
    display: flex;
    gap: var(--spacing-xs);
    opacity: 0;
    transition: opacity 0.2s ease;
}
.image-item:hover .image-actions {
    opacity: 1;
}

/* 图片预览操作按钮 */
.image-action-button {
    width: 28px;
    height: 28px;
    border-radius: var(--radius-full);
    border: none;
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}
.image-action-button:hover {
    background-color: white;
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.image-action-button svg {
    width: 16px;
    height: 16px;
}

/* 用户消息中的图片网格 */
.user-message .message-images {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-xs);
}
.user-message .message-image {
    width: 100%;
    height: 80px;
    border-radius: var(--radius-sm);
    cursor: pointer;
    object-fit: cover;
}



/* 图片全屏预览模态框 */
.image-modal {
    display: none;
    position: fixed;
    z-index: 100;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    overflow: auto;
    padding: 30px;
    box-sizing: border-box;
}
.modal-content { /* For image modal specifically */
    margin: auto;
    display: block;
    max-width: 90%;
    max-height: 80vh;
    object-fit: contain;
}
.close-modal { /* For image modal close button */
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 101; /* Above modal content */
}
.close-modal:hover {
    color: var(--primary-color);
    text-decoration: none;
}

/* 带有图像的输入框样式调整 */
.has-image #user-input {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

/* 剪贴板粘贴视觉反馈 */
.paste-highlight {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 2px rgba(49, 123, 245, 0.2) !important;
}


/* 聊天界面头部 */
.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: clamp(var(--spacing-xs), 1.5vw, var(--spacing-sm)) clamp(var(--spacing-xs), 2vw, var(--spacing-md));
    border-bottom: 1px solid var(--border-color);
    background-color: var(--card-background);
    gap: clamp(var(--spacing-xs), 1vw, var(--spacing-sm));
    box-sizing: border-box;
    overflow-x: hidden;
}

.chat-model-selector,
.chat-agent-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    min-width: 0;
    flex: 1 1 auto;
    max-width: 50%; 
}
.chat-model-selector {
    max-width: 55%; 
    flex: 3; 
}
.chat-agent-selector {
    max-width: 30%; 
    flex: 1; 
}

.chat-model-selector label,
.chat-agent-selector label {
    font-size: clamp(11px, 3vw, 12px);
    color: var(--text-secondary);
    white-space: nowrap;
    flex-shrink: 0;
}

.chat-model-selector select,
.chat-agent-selector select {
    padding: clamp(2px, 1vw, var(--spacing-xs)) clamp(4px, 1.5vw, var(--spacing-sm));
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    font-size: clamp(11px, 3vw, 12px);
    background-color: transparent;
    cursor: pointer;
    outline: none;
    transition: all 0.2s ease;
    min-width: 0;
    width: 100%; 
    text-overflow: ellipsis;
}
.chat-model-selector select:hover, .chat-agent-selector select:hover {
    border-color: var(--primary-color);
}
.chat-model-selector select:focus, .chat-agent-selector select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(49, 123, 245, 0.1);
}

/* Chat header action button (e.g., clear context) specific hover */
.chat-header .action-button:hover {
    color: var(--primary-color); /* Override default error color hover */
    background-color: var(--button-hover-bg);
}
/* Chat header close button already handled by .close-panel-btn */


/* 新增聊天界面状态消息样式 */
.chat-status {
    padding: var(--spacing-sm) var(--spacing-md);
    margin: 0 auto var(--spacing-sm) auto; /* 上0 左右auto 下SM */
    border-radius: var(--radius-md);
    font-size: 13px;
    text-align: center;
    display: none; /* 默认隐藏 */
    opacity: 0;
    transform: translateY(-20px); /* 向上移动更多 */
    transition: all 0.3s ease;
    max-width: 300px; /* 限制气泡最大宽度 */
}

.chat-status.success {
    display: block;
    opacity: 1;
    transform: translateY(-15px); /* 调整出现时的位置 */
    background-color: rgba(52, 168, 83, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(52, 168, 83, 0.3);
}

.chat-status.error {
    display: block;
    opacity: 1;
    transform: translateY(-15px); /* 调整出现时的位置 */
    background-color: rgba(234, 67, 53, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(234, 67, 53, 0.3);
}

/* 空状态消息 (if used in chat, e.g., for empty agent list, but that's in settings) */
.empty-state { /* General empty state that might be used in chat for other purposes */
    text-align: center;
    padding: var(--spacing-lg);
    color: var(--text-secondary);
}

.empty-state p {
    margin-bottom: var(--spacing-md);
    font-size: 13px;
}

/* 视频预览容器样式 */
.video-preview-container {
    margin: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-md);
    background-color: var(--card-background);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    max-width: 100%;
    box-sizing: border-box;
}

/* 视频网格布局 */
.videos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: var(--spacing-xs);
    padding: var(--spacing-xs);
}

.video-item {
    position: relative;
    height: 120px;
    border-radius: var(--radius-sm);
    overflow: hidden;
    background: #000;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.video-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.video-thumbnail {
    width: 100%;
    height: 80px;
    object-fit: cover;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.video-item:hover .video-overlay {
    opacity: 1;
}

.video-play-icon {
    color: white;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(4px);
}

.video-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.4));
    color: white;
    font-size: 11px;
    display: flex;
    align-items: center;
    padding: 0 8px;
    gap: 8px;
}

.video-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    font-weight: 500;
}

.video-size {
    font-size: 9px;
    color: #ccc;
    flex-shrink: 0;
}

.video-actions {
    position: absolute;
    top: var(--spacing-xs);
    right: var(--spacing-xs);
    display: flex;
    gap: var(--spacing-xs);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.video-item:hover .video-actions {
    opacity: 1;
}

/* 视频预览操作按钮 */
.video-action-button {
    width: 28px;
    height: 28px;
    border-radius: var(--radius-full);
    border: none;
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(4px);
}

.video-action-button:hover {
    background-color: white;
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.video-action-button svg {
    width: 16px;
    height: 16px;
}

/* 用户消息中的视频网格 */
.user-message .message-videos {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-xs);
}

.user-message .message-video {
    width: 100%;
    height: 90px;
    border-radius: var(--radius-sm);
    cursor: pointer;
    overflow: hidden;
    background: #000;
    position: relative;
}

.user-message .message-video .video-thumbnail {
    width: 100%;
    height: 70px;
    object-fit: cover;
}

.user-message .message-video .video-info {
    height: 20px;
    font-size: 10px;
    padding: 0 6px;
}

/* 统一模态框样式 */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(4px);
    }
}

.dialog-content {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    width: 90%;
    max-width: 400px;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
    animation: slideInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: scale(1);
    z-index: 10001;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.dialog-content h3 {
    font-size: 16px;
    margin-bottom: var(--spacing-md);
    color: var(--text-color);
    font-weight: 600;
}

.dialog-content p {
    margin-bottom: var(--spacing-md);
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-secondary);
}

.dialog-content input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--input-background);
    color: var(--text-color);
    font-size: 14px;
    margin-bottom: var(--spacing-lg);
    box-sizing: border-box;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
}

.dialog-content input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(49, 123, 245, 0.1);
    background-color: var(--card-background);
    transform: translateY(-1px);
}

.dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

.dialog-actions button {
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 80px;
    transform: scale(1);
    position: relative;
    overflow: hidden;
}

.dialog-actions button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.cancel-btn {
    background: var(--button-secondary-bg);
    color: var(--button-secondary-text);
}

.cancel-btn:hover {
    background: var(--button-secondary-hover);
    transform: scale(1.02);
}

.cancel-btn:hover::before {
    width: 100%;
    height: 100%;
}

.confirm-btn {
    background: var(--primary-color);
    color: white;
}

.confirm-btn:hover {
    background: var(--secondary-color);
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(49, 123, 245, 0.3);
}

.confirm-btn:hover::before {
    width: 100%;
    height: 100%;
}

.confirm-btn:disabled {
    background: var(--border-color);
    color: var(--text-secondary);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.confirm-btn:disabled::before {
    display: none;
}


.delete-btn:hover::before {
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
}

/* 已选标签栏 */
.selected-tabs-bar {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: calc(var(--spacing-sm) + 2px) var(--spacing-md); /* 增加上下内边距2px */
    background-color: rgba(255, 255, 255, 0.85); /* 半透明背景，适用于亮色模式 */
    border-bottom: 1px solid var(--border-color);
    overflow: hidden;
    max-width: 100%;
    box-sizing: border-box;
    border-top-left-radius: var(--radius-lg);
    border-top-right-radius: var(--radius-lg);
    border-top: 1px solid var(--border-color);
    white-space: nowrap;
    flex-shrink: 1;
    
    /* 增强磨玻璃效果 */
    backdrop-filter: blur(10px) saturate(130%);
    -webkit-backdrop-filter: blur(10px) saturate(130%);
    
    /* 轻微的阴影效果增强立体感 */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03) inset;
    
    /* 添加渐变边框效果 */
    background-image: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0.02) 100%);
}

/* 深色模式下已选标签栏 */
body.dark-mode .selected-tabs-bar {
    background-color: rgba(44, 46, 51, 0.85); /* 深色模式下的半透明背景，与卡片背景协调 */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) inset;
    border-color: var(--border-color);
    background-image: linear-gradient(135deg,
        rgba(60, 60, 65, 0.1) 0%,
        rgba(40, 40, 45, 0.05) 50%,
        rgba(30, 30, 35, 0.02) 100%);
}


.chat-input-area .chat-input { /* 当标签栏存在时，chat-input 的顶部边框由标签栏提供 */
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.selected-tab-chip {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background-color: rgba(255, 255, 255, 0.6); /* 更透明的背景，配合磨玻璃效果 */
    border: 1px solid rgba(233, 236, 239, 0.8); /* 半透明边框 */
    border-radius: var(--radius-full);
    padding: calc(var(--spacing-xs) + 3px) var(--spacing-sm); /* 上下内边距 */
    padding-left: calc(var(--spacing-sm) + 2px); /* 增加左侧内边距，让图标不靠近边缘 */
    font-size: 12px;
    color: var(--text-color);
    cursor: default;
    transition: all 0.2s ease;
    flex-grow: 1; /* 允许增长 */
    flex-shrink: 1; /* 允许收缩 */
    flex-basis: 0; /* 初始大小为0，让flex-grow和flex-shrink平均分配空间 */
    min-width: 40px; /* 减小最小宽度，允许更多压缩 */
    max-width: 200px; /* 最大宽度限制 */
    overflow: hidden;
    
    /* 轻微的阴影增强立体感 */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
    
    /* 磨玻璃效果 */
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    
    /* 空间不足时优化显示 */
    position: relative;
    margin: 0 1px; /* 添加小间距，提高可区分度 */
}

/* 当标签很多、空间不足时，优先保留图标和关闭按钮，文本优雅压缩 */
@media (max-width: 600px), (min-width: 601px) and (max-width: 900px) {
    .selected-tab-chip {
        padding: calc(var(--spacing-xs) + 3px) var(--spacing-xs); /* 保持增加的上下内边距 */
        padding-left: calc(var(--spacing-xs) + 4px); /* 在小屏幕上也增加左侧内边距 */
        min-width: 32px; /* 进一步减小最小宽度 */
    }
    
    /* 当宽度低于特定值时，文本可以完全隐藏，只保留图标和关闭按钮 */
    .selected-tab-chip.micro {
        min-width: 32px;
        width: 32px;
        padding: calc(var(--spacing-xs) + 3px) calc(var(--spacing-xs) / 2); /* 保持增加的上下内边距 */
        /* 微型模式下居中显示，无需特别设置左侧内边距 */
        justify-content: center;
    }
    
    .selected-tab-chip.micro span {
        display: none;
    }
}

/* 深色模式下的标签芯片 */
body.dark-mode .selected-tab-chip {
    background-color: rgba(52, 58, 64, 0.7); /* 深色模式下的半透明背景 */
    border-color: rgba(52, 58, 64, 0.9); /* 深色模式下的边框 */
    color: var(--text-color);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
}

.selected-tab-chip:hover,
.selected-tab-chip.active {
    border-color: rgba(70, 116, 255, 0.5); /* 半透明主色调边框 */
    background-color: rgba(70, 116, 255, 0.08); /* 主色调透明背景 */
    color: var(--primary-color);
    /* 不使用悬浮上抬效果，但稍微增加阴影 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
}

/* 深色模式下的悬停样式 */
body.dark-mode .selected-tab-chip:hover,
body.dark-mode .selected-tab-chip.active {
    border-color: rgba(116, 143, 252, 0.5);
    background-color: rgba(116, 143, 252, 0.15);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.selected-tab-chip img {
    width: 16px; /* 增加图标大小 */
    height: 16px; /* 增加图标大小 */
    border-radius: 3px;
    object-fit: contain;
    flex-shrink: 0;
}

.selected-tab-chip span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-grow: 1;
    min-width: 0;
    font-weight: 500; /* 稍微加粗文字 */
}

.selected-tab-chip button {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 14px;
    padding: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: var(--spacing-xs);
    cursor: pointer;
    line-height: 1;
    opacity: 0.7;
    flex-shrink: 0;
    border-radius: var(--radius-full);
    transition: all 0.2s ease;
}

.selected-tab-chip button:hover {
    color: var(--error-color);
    opacity: 1;
    background-color: rgba(201, 42, 42, 0.1);
    transform: scale(1.1); /* 稍微放大按钮增强交互感 */
}

/* 深色模式下的关闭按钮悬停效果 */
body.dark-mode .selected-tab-chip button:hover {
    background-color: rgba(255, 135, 135, 0.15);
}

.selected-tab-chip.loading {
    color: var(--primary-color);
    border-color: rgba(70, 116, 255, 0.3);
    background-color: rgba(70, 116, 255, 0.05);
}

/* 深色模式下的加载状态 */
body.dark-mode .selected-tab-chip.loading {
    border-color: rgba(116, 143, 252, 0.3);
    background-color: rgba(116, 143, 252, 0.08);
}

.selected-tab-chip.error {
    color: var(--error-color);
    border-color: rgba(201, 42, 42, 0.3);
    background-color: rgba(201, 42, 42, 0.05);
}

/* 深色模式下的错误状态 */
body.dark-mode .selected-tab-chip.error {
    border-color: rgba(255, 135, 135, 0.3);
    background-color: rgba(255, 135, 135, 0.08);
}

.tab-chip-spinner {
    width: 12px;
    height: 12px;
    border: 2px solid currentColor;
    border-top-color: transparent;
    border-radius: 50%;
    animation: chip-spinner-anim 0.8s linear infinite;
    margin-left: var(--spacing-xs);
}

@keyframes chip-spinner-anim {
    to {
        transform: rotate(360deg);
    }
}


/* 标签页选择弹窗 - 增强磨玻璃效果 */
.tab-selection-popup {
    position: fixed; /* 使用 fixed 定位 */
    z-index: 10010; /* 比 sidepanel 内其他元素高，但低于模态框等 */
    border: 1px solid rgba(233, 236, 239, 0.5); /* 半透明边框，替代 var(--border-color-glass) */
    border-radius: var(--radius-xl); /* 更大的圆角 */
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08), 0 20px 60px rgba(0, 0, 0, 0.15); /* 更深的阴影层次，替代 var(--shadow-glass) */
    max-height: 320px; /* 稍微增加高度 */
    overflow-y: auto;
    padding: 0; /* 移除内边距，避免顶部和底部留白 */
    
    /* 增强的磨玻璃效果 - 使用明确的背景颜色替代 var(--glass-bg-color) */
    background-color: rgba(255, 255, 255, 0.85); /* 半透明背景 */
    backdrop-filter: blur(16px) saturate(140%);
    -webkit-backdrop-filter: blur(16px) saturate(140%);
    
    /* 渐变边框效果 */
    background-image: linear-gradient(135deg,
        rgba(255, 255, 255, 0.15) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0.02) 100%);
    
    /* 入场动画 */
    animation: popupSlideIn 0.25s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    transform-origin: center bottom;
}

/* 深色模式下标签页选择弹窗 */
body.dark-mode .tab-selection-popup {
    border-color: rgba(52, 58, 64, 0.6);
    background-color: rgba(44, 46, 51, 0.85); /* 深色模式下的半透明背景 */
    background-image: linear-gradient(135deg,
        rgba(70, 70, 70, 0.15) 0%,
        rgba(40, 40, 40, 0.05) 50%,
        rgba(20, 20, 20, 0.02) 100%);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15), 0 20px 60px rgba(0, 0, 0, 0.25);
}

/* 弹窗入场动画 */
@keyframes popupSlideIn {
    0% {
        opacity: 0;
        transform: translateY(8px) scale(0.95);
        backdrop-filter: blur(4px) saturate(100%);
        -webkit-backdrop-filter: blur(4px) saturate(100%);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
        backdrop-filter: blur(16px) saturate(140%);
        -webkit-backdrop-filter: blur(16px) saturate(140%);
    }
}

/* 弹窗滚动条美化 */
.tab-selection-popup::-webkit-scrollbar {
    width: 6px;
}

.tab-selection-popup::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    margin: 0; /* 移除上下边距，避免留白 */
}

.tab-selection-popup::-webkit-scrollbar-thumb {
    background: rgba(70, 116, 255, 0.3);
    border-radius: var(--radius-full);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

.tab-selection-popup::-webkit-scrollbar-thumb:hover {
    background: rgba(70, 116, 255, 0.5);
}

.tab-selection-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tab-selection-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.15s ease;
    margin: 0; /* 确保没有外边距 */
}

.tab-selection-item:first-child {
    border-top-left-radius: var(--radius-xl); /* 与弹窗圆角一致 */
    border-top-right-radius: var(--radius-xl);
}

.tab-selection-item:last-child {
    border-bottom: none;
    border-bottom-left-radius: var(--radius-xl); /* 与弹窗圆角一致 */
    border-bottom-right-radius: var(--radius-xl);
}

.tab-selection-item:hover {
    background-color: var(--button-hover-bg);
    color: var(--primary-color);
}

/* .tab-selection-item.selected {
    background-color: var(--button-hover-bg);
    color: var(--primary-color);
} */

.tab-item-favicon {
    width: 16px;
    height: 16px;
    object-fit: contain;
    flex-shrink: 0;
    border-radius: 2px;
}

.tab-item-title {
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1;
    min-width: 0; /* 确保在 flex 容器中可以缩小 */
}

.tab-item-url {
    font-size: 11px;
    color: var(--text-tertiary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; /* 使用标准省略，移除渐变效果 */
    flex-shrink: 1; /* 允许 URL 缩小 */
    max-width: 40%; /* 限制 URL 最大宽度 */
    /* 移除 position: relative，不再使用渐变效果 */
}

/* 移除URL渐变效果，避免右侧留白 */
/* .tab-item-url::after 相关样式已移除 */

.tab-selection-item.no-results {
    justify-content: center;
    color: var(--text-secondary);
    cursor: default;
    padding: var(--spacing-md);
}





