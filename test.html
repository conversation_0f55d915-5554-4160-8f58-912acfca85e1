<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PageTalk 划词助手测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #4674ff;
        }
    </style>
</head>
<body>
    <h1>PageTalk 划词助手测试页面</h1>
    
    <div class="test-section">
        <h2>中文测试文本</h2>
        <p>人工智能是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。</p>
        <p>机器学习是人工智能的一个子集，专注于开发算法和统计模型，使计算机系统能够通过经验改善其在特定任务上的性能。</p>
    </div>

    <div class="test-section">
        <h2>English Test Text</h2>
        <p>Artificial intelligence (AI) is intelligence demonstrated by machines, in contrast to the natural intelligence displayed by humans and animals.</p>
        <p>Machine learning is a subset of artificial intelligence that focuses on the development of algorithms and statistical models.</p>
    </div>

    <div class="test-section">
        <h2>代码示例</h2>
        <pre><code>function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}</code></pre>
        <p>这是一个计算斐波那契数列的递归函数实现。</p>
    </div>

    <script>
        console.log('测试页面已加载');
    </script>
</body>
</html>
